import asyncio

# For real implementation, uncomment these imports:
# from langgraph.prebuilt import create_react_agent
# from langgraph.checkpoint.memory import MemorySaver
# from toolbox_langchain import ToolboxClient
# from langchain_google_vertexai import ChatVertexAI
# from langchain_google_genai import ChatGoogleGenerativeAI
# from langchain_anthropic import ChatAnthropic

prompt = """
  You're a helpful hotel assistant. You handle hotel searching, booking and
  cancellations. When the user searches for a hotel, mention it's name, id,
  location and price tier. Always mention hotel ids while performing any
  searches. This is very important for any operations. For any bookings or
  cancellations, please provide the appropriate confirmation. Be sure to
  update checkin or checkout dates if mentioned by the user.
  Don't ask for confirmations from the user.
"""

queries = [
    "Find hotels in Basel with Basel in its name.",
    "Can you book the Hilton Basel for me?",
    "Oh wait, this is too expensive. Please cancel it and book the Hyatt Regency instead.",
    "My check in dates would be from April 10, 2024 to April 19, 2024.",
]

class MockChatModel:
    """Mock chat model for testing without API keys"""

    def __init__(self, model_name="mock-model"):
        self.model_name = model_name

    def invoke(self, messages):
        # Simple mock response based on the input
        user_message = messages[-1].content if hasattr(messages[-1], 'content') else str(messages[-1])

        if "find" in user_message.lower() and "hotel" in user_message.lower():
            return type('MockResponse', (), {
                'content': "I found several hotels in Goa:\n1. Hotel Basel (ID: H001) - Downtown Basel - Budget tier\n2. Hilton Basel (ID: H002) - City Center - Luxury tier\n3. Hyatt Regency Basel (ID: H003) - Business District - Premium tier"
            })()
        elif "book" in user_message.lower() and "hilton" in user_message.lower():
            return type('MockResponse', (), {
                'content': "I've booked the Hilton Basel (ID: H002) for you. Booking confirmation: BK12345. The rate is $250/night."
            })()
        elif "cancel" in user_message.lower() and "book" in user_message.lower() and "hyatt" in user_message.lower():
            return type('MockResponse', (), {
                'content': "I've cancelled the Hilton Basel booking (BK12345) and booked the Hyatt Regency Basel (ID: H003) instead. New booking confirmation: BK12346. The rate is $180/night."
            })()
        elif "check in" in user_message.lower() or "april" in user_message.lower():
            return type('MockResponse', (), {
                'content': "I've updated your Hyatt Regency Basel booking (BK12346) with check-in date April 10, 2024 and check-out date April 19, 2024. Total stay: 9 nights."
            })()
        else:
            return type('MockResponse', (), {
                'content': "I'm a helpful hotel assistant. I can help you search for hotels, make bookings, and handle cancellations. How can I assist you today?"
            })()

async def run_application():
    # Using mock model - no API key required
    model = MockChatModel("mock-hotel-assistant")
    
    # For real implementation, uncomment one of these (requires API keys):
    # from langchain_google_vertexai import ChatVertexAI
    # model = ChatVertexAI(model_name="gemini-2.0-flash-001")

    # from langchain_google_genai import ChatGoogleGenerativeAI
    # model = ChatGoogleGenerativeAI(model="gemini-2.0-flash-001")

    # from langchain_anthropic import ChatAnthropic
    # model = ChatAnthropic(model="claude-3-5-sonnet-20240620")

    # Mock implementation - no external dependencies required
    print("=== Hotel Assistant Demo (Mock Mode) ===\n")

    for i, query in enumerate(queries, 1):
        print(f"Query {i}: {query}")
        print("-" * 50)

        # Create a mock message object
        mock_message = type('MockMessage', (), {'content': prompt + query})()

        # Get response from mock model
        response = model.invoke([mock_message])
        print(f"Assistant: {response.content}")
        print("\n" + "="*60 + "\n")

    # For real implementation with tools, uncomment this:
    
    # Load the tools from the Toolbox server
    async with ToolboxClient("http://127.0.0.1:5000") as client:
        tools = await client.aload_toolset()

        agent = create_react_agent(model, tools, checkpointer=MemorySaver())

        config = {"configurable": {"thread_id": "thread-1"}}
        for query in queries:
            inputs = {"messages": [("user", prompt + query)]}
            response = agent.invoke(inputs, stream_mode="values", config=config)
            print(response["messages"][-1].content)
    

asyncio.run(run_application())
